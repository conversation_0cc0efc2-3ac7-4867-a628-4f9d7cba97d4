<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');

// Test routes
$routes->get('test', 'TestController::index');
$routes->get('login-test', 'TestController::loginTest');
$routes->get('db-test', 'TestController::dbTest');

// Dakoii Portal Authentication Routes
$routes->group('dakoii', function($routes) {
    // Login routes
    $routes->get('/', 'DakoiiAuthController::showLoginForm');
    $routes->post('authenticate', 'DakoiiAuthController::authenticateUser');
    $routes->get('logout', 'DakoiiAuthController::logoutUser');

    // Password reset routes
    $routes->get('password-reset', 'DakoiiAuthController::requestPasswordReset');
    $routes->post('password-reset', 'DakoiiAuthController::requestPasswordReset');
    $routes->get('reset-password/(:any)', 'DakoiiAuthController::resetPassword/$1');
    $routes->post('reset-password/(:any)', 'DakoiiAuthController::resetPassword/$1');

    // Dashboard and admin routes (will be added later)
    $routes->get('dashboard', 'DakoiiDashboardController::index', ['filter' => 'dakoii_auth']);
    $routes->group('organizations', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('/', 'DakoiiOrganizationController::listOrganisations');
        $routes->get('create', 'DakoiiOrganizationController::showCreateOrganisationForm');
        $routes->post('create', 'DakoiiOrganizationController::createOrganisation');
        $routes->get('(:num)', 'DakoiiOrganizationController::viewOrganisationProfile/$1');
        $routes->get('(:num)/edit', 'DakoiiOrganizationController::showEditOrganisationModal/$1');
        $routes->post('(:num)/update', 'DakoiiOrganizationController::updateOrganisation/$1');
        $routes->post('(:num)/toggle-status', 'DakoiiOrganizationController::toggleOrganisationStatus/$1');
        $routes->post('(:num)/license-status', 'DakoiiOrganizationController::changeOrganisationLicenseStatus/$1');
        $routes->post('(:num)/upload-images', 'DakoiiOrganizationController::uploadOrganisationImages/$1');
        $routes->delete('(:num)', 'DakoiiOrganizationController::softDeleteOrganisation/$1');

        // Organization admin routes
        $routes->get('(:num)/admins', 'DakoiiOrganizationController::listOrgAdmins/$1');
        $routes->get('(:num)/admins/create', 'DakoiiOrganizationController::showCreateAdminForm/$1');
        $routes->post('(:num)/admins/create', 'DakoiiOrganizationController::createOrgAdmin/$1');
        $routes->get('admins/(:num)', 'DakoiiOrganizationController::viewOrgAdminProfile/$1');
        $routes->get('admins/(:num)/edit', 'DakoiiOrganizationController::showEditAdminModal/$1');
        $routes->post('admins/(:num)/update', 'DakoiiOrganizationController::updateOrgAdmin/$1');
        $routes->post('admins/(:num)/toggle-status', 'DakoiiOrganizationController::toggleAdminStatus/$1');
        $routes->post('admins/(:num)/reset-password', 'DakoiiOrganizationController::resetAdminPassword/$1');
        $routes->delete('admins/(:num)', 'DakoiiOrganizationController::softDeleteOrgAdmin/$1');
    });

    // Dakoii Users Management routes (Function 6)
    $routes->group('users', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('/', 'DakoiiUserController::listDakoiiUsers');
        $routes->get('create', 'DakoiiUserController::showCreateDakoiiUserForm');
        $routes->post('create', 'DakoiiUserController::createDakoiiUser');
        $routes->get('(:num)', 'DakoiiUserController::viewDakoiiUserProfile/$1');
        $routes->get('(:num)/edit', 'DakoiiUserController::showEditDakoiiUserModal/$1');
        $routes->post('(:num)/update', 'DakoiiUserController::updateDakoiiUser/$1');
        $routes->post('(:num)/toggle-status', 'DakoiiUserController::toggleDakoiiUserStatus/$1');
        $routes->post('(:num)/reset-password', 'DakoiiUserController::resetDakoiiUserPassword/$1');
        $routes->post('(:num)/resend-activation', 'DakoiiUserController::resendActivationEmail/$1');
        $routes->delete('(:num)', 'DakoiiUserController::softDeleteDakoiiUser/$1');
        $routes->post('bulk-action', 'DakoiiUserController::bulkAction');
    });

    // User activation route (public access)
    $routes->get('users/activate/(:any)', 'DakoiiUserController::completeUserActivation/$1');

    // Government Structure Management routes (Function 5)
    $routes->group('government', ['filter' => 'dakoii_auth'], function($routes) {
        $routes->get('/', 'DakoiiGovernmentController::index');
        // Countries
        $routes->get('countries', 'DakoiiGovernmentController::listCountries');
        $routes->get('countries/create', 'DakoiiGovernmentController::createCountry');
        $routes->post('countries/create', 'DakoiiGovernmentController::createCountry');
        $routes->get('countries/(:num)', 'DakoiiGovernmentController::showCountry/$1');
        $routes->get('countries/(:num)/edit', 'DakoiiGovernmentController::updateCountry/$1');
        $routes->post('countries/(:num)/edit', 'DakoiiGovernmentController::updateCountry/$1');
        $routes->post('countries/(:num)/delete', 'DakoiiGovernmentController::deleteCountry/$1');
        // Provinces
        $routes->get('provinces', 'DakoiiGovernmentController::listProvinces');
        $routes->get('provinces/create', 'DakoiiGovernmentController::createProvince');
        $routes->post('provinces/create', 'DakoiiGovernmentController::createProvince');
        $routes->get('provinces/(:num)', 'DakoiiGovernmentController::showProvince/$1');
        $routes->get('provinces/(:num)/edit', 'DakoiiGovernmentController::updateProvince/$1');
        $routes->post('provinces/(:num)/edit', 'DakoiiGovernmentController::updateProvince/$1');
        $routes->post('provinces/(:num)/delete', 'DakoiiGovernmentController::deleteProvince/$1');
        // Districts
        $routes->get('districts', 'DakoiiGovernmentController::listDistricts');
        $routes->get('districts/create', 'DakoiiGovernmentController::createDistrict');
        $routes->post('districts/create', 'DakoiiGovernmentController::createDistrict');
        $routes->get('districts/(:num)', 'DakoiiGovernmentController::showDistrict/$1');
        $routes->get('districts/(:num)/edit', 'DakoiiGovernmentController::updateDistrict/$1');
        $routes->post('districts/(:num)/edit', 'DakoiiGovernmentController::updateDistrict/$1');
        $routes->post('districts/(:num)/delete', 'DakoiiGovernmentController::deleteDistrict/$1');
        // LLGs
        $routes->get('llgs', 'DakoiiGovernmentController::listLlgs');
        $routes->get('llgs/create', 'DakoiiGovernmentController::createLlg');
        $routes->post('llgs/create', 'DakoiiGovernmentController::createLlg');
        $routes->get('llgs/(:num)', 'DakoiiGovernmentController::showLlg/$1');
        $routes->get('llgs/(:num)/edit', 'DakoiiGovernmentController::updateLlg/$1');
        $routes->post('llgs/(:num)/edit', 'DakoiiGovernmentController::updateLlg/$1');
        $routes->post('llgs/(:num)/delete', 'DakoiiGovernmentController::deleteLlg/$1');
    });
    // Hierarchy chart and map (public, no auth filter)
    $routes->get('government/chart', 'DakoiiGovernmentController::chart');
    $routes->get('government/map', 'DakoiiGovernmentController::map');
});
