<?php /** @var array $organization */ /** @var array $images */ /** @var array $stats */ ?>

<?= view('templates/dakoii_portal_template', ['title' => $title, 'page_title' => $page_title, 'user_name' => $user_name]) ?>

<div class="container mt-4">
    <h1><?= esc($organization['name']) ?></h1>
    <p><strong>Organization Code:</strong> <?= esc($organization['org_code']) ?></p>
    <p><strong>Status:</strong> <?= esc($stats['status']) ?></p>
    <p><strong>License Status:</strong> <?= esc($stats['license_status']) ?></p>
    <p><strong>Created:</strong> <?= esc($stats['created_date']) ?></p>
    <p><strong>Last Updated:</strong> <?= esc($stats['last_updated']) ?></p>

    <h2>Images</h2>
    <?php if (!empty($images)): ?>
        <div class="row">
            <?php foreach ($images as $img): ?>
                <div class="col-md-3 mb-3">
                    <img src="<?= base_url($img['image_path']) ?>" alt="Organization Image" class="img-fluid rounded">
                    <?php if (!empty($img['caption'])): ?>
                        <div><?= esc($img['caption']) ?></div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <p>No images uploaded for this organization.</p>
    <?php endif; ?>

    <a href="<?= base_url('dakoii/organizations') ?>" class="btn btn-secondary mt-3">Back to Organizations</a>
</div> 