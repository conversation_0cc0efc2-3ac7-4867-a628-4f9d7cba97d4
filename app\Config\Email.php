<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

/**
 * Email Configuration for Dakoii IMS
 *
 * Secure SSL/TLS Settings:
 * ========================
 * Server: mail.dakoiims.com
 * Username: <EMAIL>
 *
 * Port Configuration:
 * - SMTP Port: 465 (SSL/TLS)
 * - IMAP Port: 993 (SSL/TLS)
 * - POP3 Port: 995 (SSL/TLS)
 *
 * Authentication: Required for all protocols
 * Encryption: SSL/TLS (Recommended for security)
 */
class Email extends BaseConfig
{
    /**
     * From Email Address
     * Using Dakoii IMS no-reply email account
     */
    public string $fromEmail  = '<EMAIL>';

    /**
     * From Name
     * Updated to reflect Dakoii IMS branding
     */
    public string $fromName   = 'Dakoii IMS - Information Management System';

    /**
     * Default Recipients
     */
    public string $recipients = '';

    /**
     * The "user agent"
     */
    public string $userAgent = 'CodeIgniter';

    /**
     * The mail sending protocol: mail, sendmail, smtp
     * Using SMTP for secure email delivery
     */
    public string $protocol = 'smtp';

    /**
     * The server path to Sendmail.
     */
    public string $mailPath = '/usr/sbin/sendmail';

    /**
     * SMTP Server Hostname
     * Dakoii IMS secure mail server
     */
    public string $SMTPHost = 'mail.dakoiims.com';

    /**
     * SMTP Username
     * Must match the from email address for authentication
     */
    public string $SMTPUser = '<EMAIL>';

    /**
     * SMTP Password
     * Use the email account's password for authentication
     */
    public string $SMTPPass = 'dakoiianzii';

    /**
     * SMTP Port
     * Port 465 for SSL secure connection (recommended)
     * Alternative: Port 587 for TLS
     */
    public int $SMTPPort = 465;

    /**
     * SMTP Timeout (in seconds)
     * Increased timeout for secure connections
     */
    public int $SMTPTimeout = 30;

    /**
     * Enable persistent SMTP connections
     * Disabled for better security and reliability
     */
    public bool $SMTPKeepAlive = false;

    /**
     * SMTP Encryption
     * Using 'ssl' for port 465 (implicit SSL)
     * Use 'tls' for port 587 (explicit TLS/STARTTLS)
     *
     * Current setup: SSL on port 465
     */
    public string $SMTPCrypto = 'ssl';

    /**
     * Enable word-wrap
     */
    public bool $wordWrap = true;

    /**
     * Character count to wrap at
     */
    public int $wrapChars = 76;

    /**
     * Type of mail, either 'text' or 'html'
     * Using HTML for better email formatting
     */
    public string $mailType = 'html';

    /**
     * Character set (utf-8, iso-8859-1, etc.)
     */
    public string $charset = 'UTF-8';

    /**
     * Whether to validate the email address
     */
    public bool $validate = false;

    /**
     * Email Priority. 1 = highest. 5 = lowest. 3 = normal
     */
    public int $priority = 3;

    /**
     * Newline character. (Use “\r\n” to comply with RFC 822)
     */
    public string $CRLF = "\r\n";

    /**
     * Newline character. (Use “\r\n” to comply with RFC 822)
     */
    public string $newline = "\r\n";

    /**
     * Enable BCC Batch Mode.
     */
    public bool $BCCBatchMode = false;

    /**
     * Number of emails in each BCC batch
     */
    public int $BCCBatchSize = 200;

    /**
     * Enable notify message from server
     */
    public bool $DSN = false;
}
