<?php

namespace App\Controllers;

use App\Models\DakoiiUserModel;
use CodeIgniter\Controller;

/**
 * Dakoii User Management Controller
 * Function 6: Dakoii Users Management
 * 
 * Handles system administrator user management for the Dakoii portal.
 * These are distinct from organization administrators - Dakoii users are 
 * super-administrators who manage the entire platform.
 */
class DakoiiUserController extends BaseController
{
    protected $dakoiiUserModel;
    protected $session;

    public function __construct()
    {
        $this->session = \Config\Services::session();
    }

    private function getDakoiiUserModel()
    {
        if (!$this->dakoiiUserModel) {
            $this->dakoiiUserModel = new DakoiiUserModel();
        }
        return $this->dakoiiUserModel;
    }

    /**
     * List all Dakoii users with filtering and pagination
     * Task 6.1: listDakoiiUsers
     */
    public function listDakoiiUsers()
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Get search and filter parameters
        $search = $this->request->getGet('search') ?? '';
        $roleFilter = $this->request->getGet('role') ?? '';
        $statusFilter = $this->request->getGet('status') ?? '';
        $perPage = $this->request->getGet('per_page') ?? 25;

        // Build query
        $builder = $this->getDakoiiUserModel();

        // Apply search
        if (!empty($search)) {
            $builder->groupStart()
                    ->like('username', $search)
                    ->orLike('email', $search)
                    ->orLike('name', $search)
                    ->orLike('user_code', $search)
                    ->groupEnd();
        }

        // Apply role filter
        if (!empty($roleFilter)) {
            $builder->where('role', $roleFilter);
        }

        // Apply status filter
        if (!empty($statusFilter)) {
            if ($statusFilter === 'active') {
                $builder->where('is_activated', 1);
            } elseif ($statusFilter === 'inactive') {
                $builder->where('is_activated', 0);
            }
        }

        // Get paginated results
        $users = $builder->orderBy('created_at', 'DESC')->paginate($perPage);
        $pager = $builder->pager;

        // Get statistics
        $stats = [
            'total_users' => $this->getDakoiiUserModel()->countAllResults(false),
            'active_users' => $this->getDakoiiUserModel()->where('is_activated', 1)->countAllResults(false),
            'admin_users' => $this->getDakoiiUserModel()->where('role', 'admin')->countAllResults(false),
            'moderator_users' => $this->getDakoiiUserModel()->where('role', 'moderator')->countAllResults(false),
        ];

        $data = [
            'title' => 'System Users - Dakoii Portal',
            'page_title' => 'System Users',
            'users' => $users,
            'pager' => $pager,
            'stats' => $stats,
            'search' => $search,
            'roleFilter' => $roleFilter,
            'statusFilter' => $statusFilter,
            'perPage' => $perPage,
            'user_name' => $currentUser['name'],
            'current_user' => $currentUser
        ];

        return view('dakoii/dakoii_users_list', $data);
    }

    /**
     * Show create user form
     * Task 6.2: showCreateDakoiiUserForm
     */
    public function showCreateDakoiiUserForm()
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Check permissions - only admins and moderators can create users
        if (!in_array($currentUser['role'], ['admin', 'moderator'])) {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to create users.');
        }

        $data = [
            'title' => 'Create System User - Dakoii Portal',
            'page_title' => 'Create System User',
            'user_name' => $currentUser['name'],
            'current_user' => $currentUser,
            'validation' => \Config\Services::validation()
        ];

        return view('dakoii/dakoii_users_create', $data);
    }

    /**
     * Create new Dakoii user
     * Task 6.3: createDakoiiUser
     */
    public function createDakoiiUser()
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Check permissions
        if (!in_array($currentUser['role'], ['admin', 'moderator'])) {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to create users.');
        }

        // Validation rules
        $validationRules = [
            'username' => 'required|min_length[3]|max_length[50]|is_unique[dakoii_users.username]',
            'email' => 'required|valid_email|is_unique[dakoii_users.email]',
            'name' => 'required|min_length[2]|max_length[100]',
            'role' => 'required|in_list[admin,moderator,user]',
            'password' => 'required|min_length[4]',
            'password_confirm' => 'required|matches[password]'
        ];

        if (!$this->validate($validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Generate unique user code
        $userCode = $this->generateUserCode();

        // Hash password
        $passwordHash = password_hash($this->request->getPost('password'), PASSWORD_ARGON2ID);

        // Generate activation token
        $activationToken = bin2hex(random_bytes(32));

        // Prepare user data
        $userData = [
            'user_code' => $userCode,
            'username' => $this->request->getPost('username'),
            'email' => $this->request->getPost('email'),
            'name' => $this->request->getPost('name'),
            'role' => $this->request->getPost('role'),
            'password_hash' => $passwordHash,
            'activation_token' => $activationToken,
            'is_activated' => 0,
            'created_by' => $currentUserId
        ];

        // Insert user
        if ($this->getDakoiiUserModel()->insert($userData)) {
            $newUserId = $this->getDakoiiUserModel()->getInsertID();
            
            // Send activation email
            $this->sendUserActivationEmail($userData['email'], $userData['name'], $activationToken);

            return redirect()->to('/dakoii/users')->with('success', 'User created successfully. Activation email sent.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create user. Please try again.');
        }
    }

    /**
     * Generate unique user code
     * Task 6.4: generateUserCode
     */
    private function generateUserCode()
    {
        $maxAttempts = 10;
        $attempt = 0;

        do {
            // Generate 10-12 character alphanumeric code
            $length = rand(10, 12);
            $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            $code = '';
            
            for ($i = 0; $i < $length; $i++) {
                $code .= $characters[rand(0, strlen($characters) - 1)];
            }

            // Check if code exists
            $existing = $this->getDakoiiUserModel()->where('user_code', $code)->first();
            $attempt++;

        } while ($existing && $attempt < $maxAttempts);

        if ($attempt >= $maxAttempts) {
            throw new \Exception('Unable to generate unique user code after ' . $maxAttempts . ' attempts');
        }

        return $code;
    }

    /**
     * View user profile
     * Task 6.5: viewDakoiiUserProfile
     */
    public function viewDakoiiUserProfile($id)
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        $user = $this->getDakoiiUserModel()->find($id);

        if (!$user) {
            return redirect()->to('/dakoii/users')->with('error', 'User not found.');
        }

        // Check permissions - users can only view their own profile unless admin/moderator
        if ($currentUser['role'] === 'user' && $currentUserId != $id) {
            return redirect()->to('/dakoii/users')->with('error', 'You can only view your own profile.');
        }

        $data = [
            'title' => 'User Profile - ' . $user['name'],
            'page_title' => 'User Profile',
            'user' => $user,
            'user_name' => $currentUser['name'],
            'current_user' => $currentUser
        ];

        return view('dakoii/dakoii_users_profile', $data);
    }

    /**
     * Send activation email to user
     * Task 6.8: sendUserActivationEmail
     */
    private function sendUserActivationEmail($email, $name, $token)
    {
        $email_service = \Config\Services::email();
        
        $activationUrl = base_url("dakoii/users/activate/{$token}");
        
        $message = "
        <h2>Welcome to Dakoii Portal</h2>
        <p>Hello {$name},</p>
        <p>Your Dakoii Portal account has been created. Please click the link below to activate your account:</p>
        <p><a href='{$activationUrl}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Activate Account</a></p>
        <p>This link will expire in 48 hours.</p>
        <p>If you did not request this account, please ignore this email.</p>
        ";

        $email_service->setTo($email);
        $email_service->setSubject('Activate Your Dakoii Portal Account');
        $email_service->setMessage($message);

        return $email_service->send();
    }

    /**
     * Show edit user modal
     * Task 6.6: showEditDakoiiUserModal
     */
    public function showEditDakoiiUserModal($id)
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        $user = $this->getDakoiiUserModel()->find($id);

        if (!$user) {
            return redirect()->to('/dakoii/users')->with('error', 'User not found.');
        }

        // Check permissions
        $canEdit = false;
        if ($currentUser['role'] === 'admin') {
            $canEdit = true;
        } elseif ($currentUser['role'] === 'moderator' && $user['role'] !== 'admin') {
            $canEdit = true;
        } elseif ($currentUserId == $id) {
            $canEdit = true; // Users can edit their own profile (limited fields)
        }

        if (!$canEdit) {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to edit this user.');
        }

        $data = [
            'title' => 'Edit User - ' . $user['name'],
            'page_title' => 'Edit User',
            'user' => $user,
            'user_name' => $currentUser['name'],
            'current_user' => $currentUser,
            'validation' => \Config\Services::validation()
        ];

        return view('dakoii/dakoii_users_edit', $data);
    }

    /**
     * Update user information
     * Task 6.7: updateDakoiiUser
     */
    public function updateDakoiiUser($id)
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        $user = $this->getDakoiiUserModel()->find($id);

        if (!$user) {
            return redirect()->to('/dakoii/users')->with('error', 'User not found.');
        }

        // Check permissions
        $canEdit = false;
        $canEditRole = false;
        if ($currentUser['role'] === 'admin') {
            $canEdit = true;
            $canEditRole = true;
        } elseif ($currentUser['role'] === 'moderator' && $user['role'] !== 'admin') {
            $canEdit = true;
            $canEditRole = true;
        } elseif ($currentUserId == $id) {
            $canEdit = true; // Users can edit their own profile (limited fields)
        }

        if (!$canEdit) {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to edit this user.');
        }

        // Build validation rules based on permissions
        $validationRules = [
            'name' => 'required|min_length[2]|max_length[100]',
            'email' => "required|valid_email|is_unique[dakoii_users.email,id,{$id}]"
        ];

        // Only allow username changes for admins
        if ($currentUser['role'] === 'admin') {
            $validationRules['username'] = "required|min_length[3]|max_length[50]|is_unique[dakoii_users.username,id,{$id}]";
        }

        // Only allow role changes for admins and moderators (with restrictions)
        if ($canEditRole) {
            $validationRules['role'] = 'required|in_list[admin,moderator,user]';
        }

        if (!$this->validate($validationRules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        // Prepare update data
        $updateData = [
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'updated_by' => $currentUserId
        ];

        // Add username if admin
        if ($currentUser['role'] === 'admin') {
            $updateData['username'] = $this->request->getPost('username');
        }

        // Add role if permitted
        if ($canEditRole) {
            $newRole = $this->request->getPost('role');
            // Moderators cannot promote users to admin
            if ($currentUser['role'] === 'moderator' && $newRole === 'admin') {
                return redirect()->back()->with('error', 'Moderators cannot promote users to admin role.');
            }
            $updateData['role'] = $newRole;
        }

        // Handle ID photo upload
        $idPhoto = $this->request->getFile('id_photo');
        if ($idPhoto && $idPhoto->isValid() && !$idPhoto->hasMoved()) {
            $newName = $idPhoto->getRandomName();
            $idPhoto->move(ROOTPATH . 'public/uploads/users/', $newName);
            $updateData['id_photo_path'] = 'public/uploads/users/' . $newName;
        }

        // Update user
        if ($this->getDakoiiUserModel()->update($id, $updateData)) {
            return redirect()->to('/dakoii/users/' . $id)->with('success', 'User updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update user. Please try again.');
        }
    }

    /**
     * Complete user activation
     * Task 6.9: completeUserActivation
     */
    public function completeUserActivation($token)
    {
        $user = $this->getDakoiiUserModel()->where('activation_token', $token)->first();

        if (!$user) {
            return redirect()->to('/dakoii')->with('error', 'Invalid activation token.');
        }

        if ($user['is_activated']) {
            return redirect()->to('/dakoii')->with('info', 'Account is already activated. You can log in.');
        }

        // Activate user
        $updateData = [
            'is_activated' => 1,
            'activation_token' => null
        ];

        if ($this->getDakoiiUserModel()->update($user['id'], $updateData)) {
            return redirect()->to('/dakoii')->with('success', 'Account activated successfully. You can now log in.');
        } else {
            return redirect()->to('/dakoii')->with('error', 'Failed to activate account. Please try again.');
        }
    }

    /**
     * Reset user password (admin function)
     * Task 6.10: resetDakoiiUserPassword
     */
    public function resetDakoiiUserPassword($id)
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Only admins can reset passwords
        if ($currentUser['role'] !== 'admin') {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to reset passwords.');
        }

        $user = $this->getDakoiiUserModel()->find($id);

        if (!$user) {
            return redirect()->to('/dakoii/users')->with('error', 'User not found.');
        }

        // Generate temporary password
        $tempPassword = $this->generateTempPassword();
        $passwordHash = password_hash($tempPassword, PASSWORD_ARGON2ID);

        // Update user password
        $updateData = [
            'password_hash' => $passwordHash,
            'updated_by' => $currentUserId
        ];

        if ($this->getDakoiiUserModel()->update($id, $updateData)) {
            // Send email with temporary password
            $this->emailTempPassword($user['email'], $user['name'], $tempPassword);

            return redirect()->to('/dakoii/users/' . $id)->with('success', 'Password reset successfully. Temporary password sent to user email.');
        } else {
            return redirect()->back()->with('error', 'Failed to reset password. Please try again.');
        }
    }

    /**
     * Generate temporary password
     */
    private function generateTempPassword()
    {
        $characters = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
        $password = '';
        for ($i = 0; $i < 12; $i++) {
            $password .= $characters[rand(0, strlen($characters) - 1)];
        }
        return $password;
    }

    /**
     * Email temporary password
     * Task 7.1: emailTempPassword
     */
    private function emailTempPassword($email, $name, $tempPassword)
    {
        $email_service = \Config\Services::email();

        $message = "
        <h2>Password Reset - Dakoii Portal</h2>
        <p>Hello {$name},</p>
        <p>Your password has been reset by an administrator. Your temporary password is:</p>
        <p style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 18px; font-weight: bold;'>{$tempPassword}</p>
        <p><strong>Important:</strong> Please log in and change this password immediately for security reasons.</p>
        <p>This is a temporary password and should be changed upon your next login.</p>
        ";

        $email_service->setTo($email);
        $email_service->setSubject('Password Reset - Dakoii Portal');
        $email_service->setMessage($message);

        return $email_service->send();
    }

    /**
     * Toggle user status (activate/deactivate)
     * Task 6.11: toggleDakoiiUserStatus
     */
    public function toggleDakoiiUserStatus($id)
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Check permissions
        if ($currentUser['role'] !== 'admin') {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to change user status.');
        }

        $user = $this->getDakoiiUserModel()->find($id);

        if (!$user) {
            return redirect()->to('/dakoii/users')->with('error', 'User not found.');
        }

        // Cannot deactivate yourself
        if ($id == $currentUserId) {
            return redirect()->to('/dakoii/users')->with('error', 'You cannot deactivate your own account.');
        }

        // Toggle status
        $newStatus = $user['is_activated'] ? 0 : 1;
        $updateData = [
            'is_activated' => $newStatus,
            'updated_by' => $currentUserId
        ];

        if ($this->getDakoiiUserModel()->update($id, $updateData)) {
            $statusText = $newStatus ? 'activated' : 'deactivated';
            return redirect()->to('/dakoii/users')->with('success', "User {$statusText} successfully.");
        } else {
            return redirect()->back()->with('error', 'Failed to update user status. Please try again.');
        }
    }

    /**
     * Soft delete user
     * Task 6.12: softDeleteDakoiiUser
     */
    public function softDeleteDakoiiUser($id)
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Only admins can delete users
        if ($currentUser['role'] !== 'admin') {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to delete users.');
        }

        $user = $this->getDakoiiUserModel()->find($id);

        if (!$user) {
            return redirect()->to('/dakoii/users')->with('error', 'User not found.');
        }

        // Cannot delete yourself
        if ($id == $currentUserId) {
            return redirect()->to('/dakoii/users')->with('error', 'You cannot delete your own account.');
        }

        // Soft delete user
        $updateData = [
            'deleted_by' => $currentUserId
        ];

        if ($this->getDakoiiUserModel()->update($id, $updateData) && $this->getDakoiiUserModel()->delete($id)) {
            return redirect()->to('/dakoii/users')->with('success', 'User deleted successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to delete user. Please try again.');
        }
    }

    /**
     * Bulk operations for users
     */
    public function bulkAction()
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Only admins can perform bulk actions
        if ($currentUser['role'] !== 'admin') {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to perform bulk actions.');
        }

        $action = $this->request->getPost('bulk_action');
        $userIds = $this->request->getPost('user_ids');

        if (empty($action) || empty($userIds)) {
            return redirect()->to('/dakoii/users')->with('error', 'Please select an action and users.');
        }

        // Remove current user from bulk actions
        $userIds = array_filter($userIds, function($id) use ($currentUserId) {
            return $id != $currentUserId;
        });

        if (empty($userIds)) {
            return redirect()->to('/dakoii/users')->with('error', 'Cannot perform bulk actions on your own account.');
        }

        $successCount = 0;
        $totalCount = count($userIds);

        switch ($action) {
            case 'activate':
                foreach ($userIds as $id) {
                    if ($this->getDakoiiUserModel()->update($id, ['is_activated' => 1, 'updated_by' => $currentUserId])) {
                        $successCount++;
                    }
                }
                break;

            case 'deactivate':
                foreach ($userIds as $id) {
                    if ($this->getDakoiiUserModel()->update($id, ['is_activated' => 0, 'updated_by' => $currentUserId])) {
                        $successCount++;
                    }
                }
                break;

            case 'delete':
                foreach ($userIds as $id) {
                    if ($this->getDakoiiUserModel()->update($id, ['deleted_by' => $currentUserId]) &&
                        $this->getDakoiiUserModel()->delete($id)) {
                        $successCount++;
                    }
                }
                break;

            default:
                return redirect()->to('/dakoii/users')->with('error', 'Invalid bulk action.');
        }

        if ($successCount > 0) {
            return redirect()->to('/dakoii/users')->with('success', "Bulk action completed successfully on {$successCount} of {$totalCount} users.");
        } else {
            return redirect()->to('/dakoii/users')->with('error', 'Bulk action failed. Please try again.');
        }
    }

    /**
     * Resend activation email
     */
    public function resendActivationEmail($id)
    {
        $currentUserId = $this->session->get('dakoii_user_id');
        $currentUser = $this->getDakoiiUserModel()->find($currentUserId);

        if (!$currentUser) {
            return redirect()->to('/dakoii/logout');
        }

        // Check permissions
        if (!in_array($currentUser['role'], ['admin', 'moderator'])) {
            return redirect()->to('/dakoii/users')->with('error', 'You do not have permission to resend activation emails.');
        }

        $user = $this->getDakoiiUserModel()->find($id);

        if (!$user) {
            return redirect()->to('/dakoii/users')->with('error', 'User not found.');
        }

        if ($user['is_activated']) {
            return redirect()->to('/dakoii/users')->with('error', 'User is already activated.');
        }

        // Generate new activation token
        $activationToken = bin2hex(random_bytes(32));

        $updateData = [
            'activation_token' => $activationToken,
            'updated_by' => $currentUserId
        ];

        if ($this->getDakoiiUserModel()->update($id, $updateData)) {
            // Send activation email
            $this->sendUserActivationEmail($user['email'], $user['name'], $activationToken);

            return redirect()->to('/dakoii/users')->with('success', 'Activation email sent successfully.');
        } else {
            return redirect()->back()->with('error', 'Failed to resend activation email. Please try again.');
        }
    }
}
